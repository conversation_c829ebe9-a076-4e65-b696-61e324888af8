﻿Log file open, 08/26/25 00:07:49
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=28652)
LogWindows: Enabling Tpause support
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: Auracron
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Control listening on port 37904
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.6-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.6.1-44394996+++UE5+Release-5.6"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (24H2) [10.0.26100.4946] "
LogCsvProfiler: Display: Metadata set : cpu="GenuineIntel|13th Gen Intel(R) Core(TM) i5-1345U"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" C:\Game\Auracron\Auracron.uproject -compile""
LogCsvProfiler: Display: Metadata set : loginid="8bb1964343e8298f803f869f44351803"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.304147
LogCsvProfiler: Display: Metadata set : systemresolution.resx="1280"
LogCsvProfiler: Display: Metadata set : systemresolution.resy="720"
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: -3:00, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-99B851D541D0303003C5A18D9DDBDE69
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [../../../../../../Game/Auracron/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogAssetRegistry: Display: No AssetDiscovery cache present at ../../../../../../Game/Auracron/Intermediate/CachedAssetRegistryDiscovery.bin. AssetRegistry discovery of files will be uncached.
LogPluginManager: Looking for build plugins target receipt
LogPluginManager: Unable to find target receipt in path: ../../../../../../Game/Auracron/Binaries/Win64/*.target
LogConfig: Display: Loading Android ini files took 0.06 seconds
LogConfig: Display: Loading IOS ini files took 0.07 seconds
LogConfig: Display: Loading Mac ini files took 0.07 seconds
LogConfig: Display: Loading VulkanPC ini files took 0.07 seconds
LogConfig: Display: Loading Windows ini files took 0.07 seconds
LogConfig: Display: Loading Unix ini files took 0.08 seconds
LogConfig: Display: Loading TVOS ini files took 0.08 seconds
LogConfig: Display: Loading Linux ini files took 0.09 seconds
LogConfig: Display: Loading VisionOS ini files took 0.04 seconds
LogPluginManager: Found matching target receipt: ../../../Engine/Binaries/Win64/UnrealEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogPluginManager: Unable to find target receipt in path: ../../../../../../Game/Auracron/Binaries/Win64/*.target
LogPluginManager: Found matching target receipt: ../../../Engine/Binaries/Win64/UnrealEditor.target
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosInsights
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin IoStoreInsights
LogPluginManager: Mounting Engine plugin MassInsights
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin PCG
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin GameplayInsights
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin TweeningUtils
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin NamingTokens
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin ProjectLauncher
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin ChaosVehiclesPlugin
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin EditorDataStorageFeatures
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin FieldSystemPlugin
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin TargetingSystem
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin GeometryDataflow
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin LevelSequenceNavigatorBridge
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin SequenceNavigator
LogPluginManager: Mounting Engine plugin RuntimeTelemetry
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin DataRegistry
LogPluginManager: Mounting Engine plugin GameplayAbilities
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin Cascade
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MassGameplay
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin PropertyBindingUtils
LogPluginManager: Mounting Engine plugin ReplicationGraph
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin SmartObjects
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin WorldConditions
LogPluginManager: Mounting Engine plugin ZoneGraph
LogPluginManager: Mounting Engine plugin ZoneGraphAnnotations
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin CompositeCore
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Project plugin UnrealMCP
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
Launching UnrealBuildTool... [C:/Program Files/Epic Games/UE_5.6/Engine/Build/BatchFiles/Build.bat -Mode=QueryTargets -Project="C:/Game/Auracron/Auracron.uproject" -Output="C:/Game/Auracron/Intermediate/TargetInfo.json" -IncludeAllTargets -DontIncludeParentAssembly]
LogEOSShared: Loaded "C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/EOSSDK-Win64-Shipping.dll"
LogEOSShared: FEOSSDKManager::Initialize Initializing EOSSDK Version:1.17.0-41373641
LogInit: Using libcurl 8.12.1
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_TLSAUTH_SRP
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum 256 concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogStudioTelemetry: Started StudioTelemetry Session
LogNFORDenoise: NFORDenoise function starting up
LogConfig: Applying CVar settings from Section [/Script/CompositeCore.CompositeCorePluginSettings] File [Engine]
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.6-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=8bb1964343e8298f803f869f44351803
LogInit: DeviceId=
LogInit: Engine Version: 5.6.1-44394996+++UE5+Release-5.6
LogInit: Compatible Engine Version: 5.6.0-43139311+++UE5+Release-5.6
LogInit: Net CL: 43139311
LogInit: OS: Windows 11 (24H2) [10.0.26100.4946] (), CPU: 13th Gen Intel(R) Core(TM) i5-1345U, GPU: Intel(R) Iris(R) Xe Graphics
LogInit: Compiled (64-bit): Jul 28 2025 20:53:34
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.6
LogInit: Command Line: -compile
LogInit: Base Directory: C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 37
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 207
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 17
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 11
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 121
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 56
LogDevObjectVersion:   UE5-SpecialProject (59DA5D52-1232-4948-B878-597870B8E98B): 9
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   LensFileVersion (8652A554-966A-466C-9FD7-1C6DD61B1ADB): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.08.26-03.08.14:234][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[r.AllowStaticLighting:0]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[r.RayTracing:1]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[r.RayTracing.RayTracingProxies.ProjectEnabled:1]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.08.26-03.08.14:234][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.08.26-03.08.14:234][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.08.26-03.08.14:234][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.08.26-03.08.14:234][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.08.26-03.08.14:234][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.08.26-03.08.14:236][  0]LogCsvProfiler: Display: Metadata set : systemresolution.resx="1536"
[2025.08.26-03.08.14:236][  0]LogCsvProfiler: Display: Metadata set : systemresolution.resy="864"
[2025.08.26-03.08.14:236][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.08.26-03.08.14:236][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.08.26-03.08.14:236][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.08.26-03.08.14:236][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.SkylightIntensityMultiplier:1.0]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.08.26-03.08.14:236][  0]LogConfig: Set CVar [[r.LumenScene.DirectLighting.MaxLightsPerTile:8]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.LumenScene.DirectLighting.UpdateFactor:32]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.UpdateFactor:64]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:100]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.NumAdaptiveProbes:8]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.BentNormal:1]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:70]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.08.26-03.08.14:237][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.NumSamples:5]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.08.26-03.08.14:237][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.08.26-03.08.14:237][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.08.26-03.08.14:237][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:2]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:256]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:256]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.MaxSampleCount:8]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.UseExistenceMask:0]]
[2025.08.26-03.08.14:237][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.08.26-03.08.14:237][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.08.26-03.08.14:237][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.08.26-03.08.14:237][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.08.26-03.08.14:240][  0]LogRHI: Using Default RHI: D3D12
[2025.08.26-03.08.14:240][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.08.26-03.08.14:240][  0]LogRHI: Loading RHI module D3D12RHI
[2025.08.26-03.08.14:243][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.08.26-03.08.14:243][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.08.26-03.08.14:354][  0]LogD3D12RHI: Intel Extensions Framework not supported by driver. Please check if a driver update is available.
[2025.08.26-03.08.14:373][  0]LogD3D12RHI: Found D3D12 adapter 0: Intel(R) Iris(R) Xe Graphics (VendorId: 8086, DeviceId: a7a1, SubSysId: c001028, Revision: 0004
[2025.08.26-03.08.14:373][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.7, binding tier 3, wave ops supported, atomic64 unsupported
[2025.08.26-03.08.14:373][  0]LogD3D12RHI:   Adapter has 128MB of dedicated video memory, 0MB of dedicated system memory, and 16226MB of shared system memory, 1 output[s], UMA:true
[2025.08.26-03.08.14:373][  0]LogD3D12RHI:   Driver Version: 32.0.101.6556 (internal:32.0.101.6556, unified:101.6556)
[2025.08.26-03.08.14:373][  0]LogD3D12RHI:      Driver Date: 1-23-2025
[2025.08.26-03.08.14:382][  0]LogD3D12RHI: Found D3D12 adapter 1: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.08.26-03.08.14:382][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.08.26-03.08.14:383][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 16226MB of shared system memory, 0 output[s], UMA:true
[2025.08.26-03.08.14:383][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.08.26-03.08.14:383][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.08.26-03.08.14:383][  0]LogD3D12RHI: Adapter only supports up to Feature Level 'SM5', requested Feature Level was 'SM6'
[2025.08.26-03.08.14:383][  0]LogRHI: RHI D3D12 with Feature Level SM6 is not supported on your system, attempting to fall back to RHI D3D11 with Feature Level SM5
[2025.08.26-03.08.14:383][  0]LogRHI: Loading RHI module D3D11RHI
[2025.08.26-03.08.14:384][  0]LogRHI: Checking if RHI D3D11 with Feature Level SM5 is supported by your system.
[2025.08.26-03.08.14:384][  0]LogD3D11RHI: D3D11 min allowed feature level: 11_0
[2025.08.26-03.08.14:384][  0]LogD3D11RHI: D3D11 max allowed feature level: 11_1
[2025.08.26-03.08.14:384][  0]LogD3D11RHI: D3D11 adapters:
[2025.08.26-03.08.14:384][  0]LogD3D11RHI: Testing D3D11 Adapter 0:
[2025.08.26-03.08.14:384][  0]LogD3D11RHI:     Description : Intel(R) Iris(R) Xe Graphics
[2025.08.26-03.08.14:384][  0]LogD3D11RHI:     VendorId    : 8086
[2025.08.26-03.08.14:384][  0]LogD3D11RHI:     DeviceId    : a7a1
[2025.08.26-03.08.14:384][  0]LogD3D11RHI:     SubSysId    : c001028
[2025.08.26-03.08.14:384][  0]LogD3D11RHI:     Revision    : 0004
[2025.08.26-03.08.14:384][  0]LogD3D11RHI:     DedicatedVideoMemory : 134217728 bytes
[2025.08.26-03.08.14:384][  0]LogD3D11RHI:     DedicatedSystemMemory : 0 bytes
[2025.08.26-03.08.14:384][  0]LogD3D11RHI:     SharedSystemMemory : 17014562816 bytes
[2025.08.26-03.08.14:384][  0]LogD3D11RHI:     AdapterLuid : 0 310376352
[2025.08.26-03.08.14:677][  0]LogD3D11RHI:    0. 'Intel(R) Iris(R) Xe Graphics' (Feature Level 11_1)
[2025.08.26-03.08.14:677][  0]LogD3D11RHI:       128/0/16226 MB DedicatedVideo/DedicatedSystem/SharedSystem, Outputs:1, VendorId:0x8086 UMA:true
[2025.08.26-03.08.14:677][  0]LogD3D11RHI: Testing D3D11 Adapter 1:
[2025.08.26-03.08.14:677][  0]LogD3D11RHI:     Description : Microsoft Basic Render Driver
[2025.08.26-03.08.14:677][  0]LogD3D11RHI:     VendorId    : 1414
[2025.08.26-03.08.14:677][  0]LogD3D11RHI:     DeviceId    : 008c
[2025.08.26-03.08.14:677][  0]LogD3D11RHI:     SubSysId    : 0000
[2025.08.26-03.08.14:677][  0]LogD3D11RHI:     Revision    : 0000
[2025.08.26-03.08.14:677][  0]LogD3D11RHI:     DedicatedVideoMemory : 0 bytes
[2025.08.26-03.08.14:677][  0]LogD3D11RHI:     DedicatedSystemMemory : 0 bytes
[2025.08.26-03.08.14:677][  0]LogD3D11RHI:     SharedSystemMemory : 17014562816 bytes
[2025.08.26-03.08.14:677][  0]LogD3D11RHI:     AdapterLuid : 0 85646
[2025.08.26-03.08.14:682][  0]LogD3D11RHI:    1. 'Microsoft Basic Render Driver' (Feature Level 11_1)
[2025.08.26-03.08.14:682][  0]LogD3D11RHI:       0/0/16226 MB DedicatedVideo/DedicatedSystem/SharedSystem, Outputs:0, VendorId:0x1414 UMA:true
[2025.08.26-03.08.14:682][  0]LogD3D11RHI: Chosen D3D11 Adapter:
[2025.08.26-03.08.14:682][  0]LogD3D11RHI:     Description : Intel(R) Iris(R) Xe Graphics
[2025.08.26-03.08.14:682][  0]LogD3D11RHI:     VendorId    : 8086
[2025.08.26-03.08.14:682][  0]LogD3D11RHI:     DeviceId    : a7a1
[2025.08.26-03.08.14:682][  0]LogD3D11RHI:     SubSysId    : c001028
[2025.08.26-03.08.14:682][  0]LogD3D11RHI:     Revision    : 0004
[2025.08.26-03.08.14:682][  0]LogD3D11RHI:     DedicatedVideoMemory : 134217728 bytes
[2025.08.26-03.08.14:682][  0]LogD3D11RHI:     DedicatedSystemMemory : 0 bytes
[2025.08.26-03.08.14:682][  0]LogD3D11RHI:     SharedSystemMemory : 17014562816 bytes
[2025.08.26-03.08.14:682][  0]LogD3D11RHI:     AdapterLuid : 0 310376352
[2025.08.26-03.08.14:682][  0]LogD3D11RHI: Integrated GPU (iGPU): true
[2025.08.26-03.08.14:682][  0]LogRHI: RHI D3D11 with Feature Level SM5 is supported and will be used.
[2025.08.26-03.08.14:682][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.08.26-03.08.14:682][  0]LogHAL: Display: Platform has ~ 32 GB [34029125632 / 34359738368 / 32], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.08.26-03.08.14:682][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.08.26-03.08.14:682][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.26-03.08.14:683][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.08.26-03.08.14:683][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.08.26-03.08.14:683][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.08.26-03.08.14:683][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.08.26-03.08.14:683][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.08.26-03.08.14:683][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.08.26-03.08.14:683][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.08.26-03.08.14:683][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.08.26-03.08.14:683][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.08.26-03.08.14:683][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.08.26-03.08.14:683][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [C:/Game/Auracron/Saved/Config/WindowsEditor/Editor.ini]
[2025.08.26-03.08.14:683][  0]LogInit: Computer: TKT
[2025.08.26-03.08.14:683][  0]LogInit: User: tktca
[2025.08.26-03.08.14:683][  0]LogInit: CPU Page size=4096, Cores=10
[2025.08.26-03.08.14:683][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.08.26-03.08.14:683][  0]LogMemory: Process is running as part of a Windows Job with separate resource limits
[2025.08.26-03.08.14:683][  0]LogMemory: Memory total: Physical=31.7GB (32GB approx) Virtual=45.9GB
[2025.08.26-03.08.14:683][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.08.26-03.08.14:683][  0]LogMemory: Process Physical Memory: 574.85 MB used, 637.46 MB peak
[2025.08.26-03.08.14:683][  0]LogMemory: Process Virtual Memory: 555.58 MB used, 597.73 MB peak
[2025.08.26-03.08.14:683][  0]LogMemory: Physical Memory: 15302.22 MB used,  17150.48 MB free, 32452.70 MB total
[2025.08.26-03.08.14:683][  0]LogMemory: Virtual Memory: 22393.46 MB used,  24637.75 MB free, 47031.21 MB total
[2025.08.26-03.08.14:683][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.08.26-03.08.14:686][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.08.26-03.08.14:688][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.08.26-03.08.14:688][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.08.26-03.08.14:689][  0]LogInit: Using OS detected language (pt-BR).
[2025.08.26-03.08.14:689][  0]LogInit: Using OS detected locale (pt-BR).
[2025.08.26-03.08.14:697][  0]LogInit: Setting process to per monitor DPI aware
[2025.08.26-03.08.15:210][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Editor/pt/Editor.locres' could not be opened for reading!
[2025.08.26-03.08.15:210][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/EditorTutorials/pt/EditorTutorials.locres' could not be opened for reading!
[2025.08.26-03.08.15:210][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Keywords/pt/Keywords.locres' could not be opened for reading!
[2025.08.26-03.08.15:210][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Category/pt/Category.locres' could not be opened for reading!
[2025.08.26-03.08.15:210][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/ToolTips/pt/ToolTips.locres' could not be opened for reading!
[2025.08.26-03.08.15:210][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/PropertyNames/pt/PropertyNames.locres' could not be opened for reading!
[2025.08.26-03.08.15:210][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Engine/pt/Engine.locres' could not be opened for reading!
[2025.08.26-03.08.15:211][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/MetaHuman/MetaHumanSDK/Content/Localization/MetaHumanSDK/pt/MetaHumanSDK.locres' could not be opened for reading!
[2025.08.26-03.08.15:211][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/OnlineSubsystem/Content/Localization/OnlineSubsystem/pt/OnlineSubsystem.locres' could not be opened for reading!
[2025.08.26-03.08.15:211][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/OnlineSubsystemUtils/Content/Localization/OnlineSubsystemUtils/pt/OnlineSubsystemUtils.locres' could not be opened for reading!
[2025.08.26-03.08.15:211][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/IOS/OnlineSubsystemIOS/Content/Localization/OnlineSubsystemIOS/pt/OnlineSubsystemIOS.locres' could not be opened for reading!
[2025.08.26-03.08.15:211][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/Android/OnlineSubsystemGooglePlay/Content/Localization/OnlineSubsystemGooglePlay/pt/OnlineSubsystemGooglePlay.locres' could not be opened for reading!
[2025.08.26-03.08.15:339][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.08.26-03.08.15:339][  0]LogWindowsTextInputMethodSystem:   - Português (Brasil) - (Keyboard).
[2025.08.26-03.08.15:339][  0]LogWindowsTextInputMethodSystem:   - Português (Portugal) - (Keyboard).
[2025.08.26-03.08.15:339][  0]LogWindowsTextInputMethodSystem: Activated input method: Português (Brasil) - (Keyboard).
[2025.08.26-03.08.15:341][  0]LogWindowsTouchpad: Display: CacheForceMaxTouchpadSensitivityMode SetMaxTouchpadSensitivity
[2025.08.26-03.08.15:343][  0]LogObj: Display: Attempting to load config data for Default__SlateThemeManager before the Class has been constructed/registered/linked (likely during module loading or early startup). This will result in the load silently failing and should be fixed.
[2025.08.26-03.08.15:348][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.08.26-03.08.15:348][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.08.26-03.08.15:499][  0]LogRHI: Using Default RHI: D3D12
[2025.08.26-03.08.15:499][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.08.26-03.08.15:499][  0]LogRHI: Loading RHI module D3D12RHI
[2025.08.26-03.08.15:499][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.08.26-03.08.15:499][  0]LogD3D12RHI: Adapter only supports up to Feature Level 'SM5', requested Feature Level was 'SM6'
[2025.08.26-03.08.15:499][  0]LogRHI: RHI D3D12 with Feature Level SM6 is not supported on your system, attempting to fall back to RHI D3D11 with Feature Level SM5
[2025.08.26-03.08.15:499][  0]LogRHI: Loading RHI module D3D11RHI
[2025.08.26-03.08.15:499][  0]LogRHI: Checking if RHI D3D11 with Feature Level SM5 is supported by your system.
[2025.08.26-03.08.15:499][  0]LogRHI: RHI D3D11 with Feature Level SM5 is supported and will be used.
[2025.08.26-03.08.15:500][  0]LogWindows: Attached monitors:
[2025.08.26-03.08.15:500][  0]LogWindows:     resolution: 1920x1080, work area: (0, 0) -> (1920, 1020), device: '\\.\DISPLAY6' [PRIMARY]
[2025.08.26-03.08.15:500][  0]LogWindows: Found 1 attached monitors.
[2025.08.26-03.08.15:500][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.08.26-03.08.15:501][  0]LogRHI: RHI Adapter Info:
[2025.08.26-03.08.15:501][  0]LogRHI:             Name: Intel(R) Iris(R) Xe Graphics
[2025.08.26-03.08.15:501][  0]LogRHI:   Driver Version: 32.0.101.6556 (internal:32.0.101.6556, unified:101.6556)
[2025.08.26-03.08.15:501][  0]LogRHI:      Driver Date: 1-23-2025
[2025.08.26-03.08.15:501][  0]LogD3D11RHI: Creating new Direct3DDevice
[2025.08.26-03.08.15:501][  0]LogD3D11RHI:     GPU DeviceId: 0xa7a1 (for the marketing name, search the web for "GPU Device Id")
[2025.08.26-03.08.15:501][  0]LogRHI: Texture pool is 1523 MB (70% of 2176 MB)
[2025.08.26-03.08.15:501][  0]LogNvidiaAftermath: Nvidia Aftermath is disabled in D3D11 due to instability issues.
[2025.08.26-03.08.15:501][  0]LogD3D11RHI: Creating D3DDevice using adapter:
[2025.08.26-03.08.15:501][  0]LogD3D11RHI:     Description : Intel(R) Iris(R) Xe Graphics
[2025.08.26-03.08.15:501][  0]LogD3D11RHI:     VendorId    : 8086
[2025.08.26-03.08.15:501][  0]LogD3D11RHI:     DeviceId    : a7a1
[2025.08.26-03.08.15:501][  0]LogD3D11RHI:     SubSysId    : c001028
[2025.08.26-03.08.15:501][  0]LogD3D11RHI:     Revision    : 0004
[2025.08.26-03.08.15:501][  0]LogD3D11RHI:     DedicatedVideoMemory : 134217728 bytes
[2025.08.26-03.08.15:501][  0]LogD3D11RHI:     DedicatedSystemMemory : 0 bytes
[2025.08.26-03.08.15:501][  0]LogD3D11RHI:     SharedSystemMemory : 17014562816 bytes
[2025.08.26-03.08.15:501][  0]LogD3D11RHI:     AdapterLuid : 0 310376352
[2025.08.26-03.08.15:735][  0]LogNvidiaAftermath: Aftermath is not loaded.
[2025.08.26-03.08.15:765][  0]LogD3D11RHI: Intel Extensions loaded requested version for UAVOverlap: 1.1.0
[2025.08.26-03.08.15:765][  0]LogD3D11RHI: Intel Extensions loaded requested version Atomics Version: 3.4.1
[2025.08.26-03.08.15:766][  0]LogD3D11RHI: Intel Extensions Framework enabled
[2025.08.26-03.08.15:766][  0]LogD3D11RHI: RHI has support for 64 bit atomics
[2025.08.26-03.08.15:766][  0]LogD3D11RHI: Async texture creation enabled
[2025.08.26-03.08.15:766][  0]LogD3D11RHI: D3D11_MAP_WRITE_NO_OVERWRITE for dynamic buffer SRVs is supported
[2025.08.26-03.08.15:766][  0]LogD3D11RHI: Array index from any shader is supported
[2025.08.26-03.08.15:777][  0]LogVRS: Current RHI does not support Variable Rate Shading
[2025.08.26-03.08.15:779][  0]LogCsvProfiler: Display: Metadata set : verbatimrhiname="D3D11"
[2025.08.26-03.08.15:779][  0]LogCsvProfiler: Display: Metadata set : rhiname="D3D11"
[2025.08.26-03.08.15:779][  0]LogCsvProfiler: Display: Metadata set : rhifeaturelevel="SM5"
[2025.08.26-03.08.15:779][  0]LogCsvProfiler: Display: Metadata set : shaderplatform="PCD3D_SM5"
[2025.08.26-03.08.15:779][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.08.26-03.08.15:783][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="C:/Game/Auracron/Auracron.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Game/Auracron/Intermediate/TurnkeyReport_0.log" -log="C:/Game/Auracron/Intermediate/TurnkeyLog_0.log" -project="C:/Game/Auracron/Auracron.uproject"  -platform=all'
[2025.08.26-03.08.15:783][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""C:/Program Files/Epic Games/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="C:/Game/Auracron/Auracron.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Game/Auracron/Intermediate/TurnkeyReport_0.log" -log="C:/Game/Auracron/Intermediate/TurnkeyLog_0.log" -project="C:/Game/Auracron/Auracron.uproject"  -platform=all" ]
[2025.08.26-03.08.15:794][  0]LogTextureFormatASTC: Display: ASTCEnc version 5.0.1 library loaded
[2025.08.26-03.08.15:794][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.08.26-03.08.15:794][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.08.26-03.08.15:794][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.08.26-03.08.15:794][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.08.26-03.08.15:795][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.08.26-03.08.15:795][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.13
[2025.08.26-03.08.15:795][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.13.dll
[2025.08.26-03.08.15:795][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.08.26-03.08.15:795][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.08.26-03.08.15:829][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.08.26-03.08.15:829][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.08.26-03.08.15:829][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.08.26-03.08.15:829][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.08.26-03.08.15:829][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_OpenXR'
[2025.08.26-03.08.15:829][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.08.26-03.08.15:829][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.08.26-03.08.15:829][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.08.26-03.08.15:829][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.08.26-03.08.15:829][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_OpenXRClient'
[2025.08.26-03.08.15:829][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.08.26-03.08.15:829][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.08.26-03.08.15:843][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.08.26-03.08.15:844][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.08.26-03.08.15:858][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.08.26-03.08.15:858][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.08.26-03.08.15:858][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.08.26-03.08.15:858][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.08.26-03.08.15:873][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.08.26-03.08.15:873][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.08.26-03.08.15:873][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.08.26-03.08.15:873][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.08.26-03.08.15:887][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.08.26-03.08.15:887][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.08.26-03.08.15:901][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.08.26-03.08.15:901][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.08.26-03.08.15:901][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.08.26-03.08.15:901][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.08.26-03.08.15:901][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.08.26-03.08.15:938][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL_ES3_1_IOS from hinted modules, loading all potential format modules to find it
[2025.08.26-03.08.15:946][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.08.26-03.08.15:946][  0]LogTargetPlatformManager:   SF_METAL_ES3_1_IOS
[2025.08.26-03.08.15:946][  0]LogTargetPlatformManager:   SF_METAL_SM5_IOS
[2025.08.26-03.08.15:946][  0]LogTargetPlatformManager:   SF_METAL_ES3_1_TVOS
[2025.08.26-03.08.15:946][  0]LogTargetPlatformManager:   SF_METAL_SM5_TVOS
[2025.08.26-03.08.15:946][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.08.26-03.08.15:946][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.08.26-03.08.15:946][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.08.26-03.08.15:946][  0]LogTargetPlatformManager:   SF_METAL_ES3_1
[2025.08.26-03.08.15:946][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.08.26-03.08.15:946][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.08.26-03.08.15:946][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.08.26-03.08.15:946][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.08.26-03.08.15:946][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.08.26-03.08.15:946][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.08.26-03.08.15:946][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.08.26-03.08.15:946][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.08.26-03.08.15:946][  0]LogTargetPlatformManager:   VVM_1_0
[2025.08.26-03.08.15:946][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.08.26-03.08.15:946][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.08.26-03.08.15:946][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.08.26-03.08.15:946][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.08.26-03.08.15:946][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.08.26-03.08.15:946][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.08.26-03.08.15:946][  0]LogRendererCore: Ray tracing is disabled. Reason: not supported by current RHI.
[2025.08.26-03.08.15:949][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.08.26-03.08.15:949][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file ../../../../../../Game/Auracron/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.08.26-03.08.15:949][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.08.26-03.08.15:949][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file ../../../../../../Game/Auracron/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.08.26-03.08.15:949][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.08.26-03.08.16:157][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1350 MiB)
[2025.08.26-03.08.16:157][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.08.26-03.08.16:157][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.08.26-03.08.16:160][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.08.26-03.08.16:160][  0]LogZenServiceInstance: InTree version at 'C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.08.26-03.08.16:160][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.08.26-03.08.16:161][  0]LogZenServiceInstance: No current process using the data dir found, launching a new instance
[2025.08.26-03.08.16:161][  0]LogZenServiceInstance: Display: Launching executable 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe', working dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install', data dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data', args '--port 8558 --data-dir "C:\Users\<USER>\AppData\Local\UnrealEngine\Common\Zen\Data" --http asio --gc-cache-duration-seconds 1209600 --gc-interval-seconds 21600 --gc-low-diskspace-threshold 2147483648 --quiet --http-forceloopback --owner-pid 30504  --child-id Zen_30504_Startup'
[2025.08.26-03.08.16:337][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.08.26-03.08.16:337][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.178 seconds
[2025.08.26-03.08.16:339][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.08.26-03.08.16:349][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.01 seconds.
[2025.08.26-03.08.16:350][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.03ms. RandomReadSpeed=868.87MBs, RandomWriteSpeed=109.34MBs. Assigned SpeedClass 'Local'
[2025.08.26-03.08.16:351][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.08.26-03.08.16:351][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.08.26-03.08.16:351][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.08.26-03.08.16:351][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.08.26-03.08.16:351][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.08.26-03.08.16:351][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.08.26-03.08.16:351][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.08.26-03.08.16:352][  0]LogShaderCompilers: Guid format shader working directory is 14 characters bigger than the processId version (../../../../../../Game/Auracron/Intermediate/Shaders/WorkingDirectory/30504/).
[2025.08.26-03.08.16:352][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/1D143366463F1905BE477E83FBCDCFDF/'.
[2025.08.26-03.08.16:353][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.08.26-03.08.16:353][  0]LogUbaHorde: Display: UBA/Horde Configuration [Uba.Provider.Horde]: Not Enabled
[2025.08.26-03.08.16:353][  0]LogShaderCompilers: Display: Using 9 local workers for shader compilation
[2025.08.26-03.08.16:356][  0]LogShaderCompilers: Display: Compiling shader autogen file: ../../../../../../Game/Auracron/Intermediate/ShaderAutogen/PCD3D_SM5/AutogenShaderHeaders.ush
[2025.08.26-03.08.16:357][  0]LogShaderCompilers: Display: Failed to delete old shader autogen file: ../../../../../../Game/Auracron/Intermediate/ShaderAutogen/PCD3D_SM5/AutogenShaderHeaders.ush
[2025.08.26-03.08.16:358][  0]LogShaderCompilers: Display: Shader autogen file written: ../../../../../../Game/Auracron/Intermediate/ShaderAutogen/PCD3D_SM5/AutogenShaderHeaders.ush
[2025.08.26-03.08.17:917][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.08.26-03.08.18:096][  0]LogSlate: Using FreeType 2.10.0
[2025.08.26-03.08.18:096][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.08.26-03.08.18:099][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.08.26-03.08.18:099][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.08.26-03.08.18:099][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png]
[2025.08.26-03.08.18:099][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png] file: [../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png]
[2025.08.26-03.08.18:115][  0]LogAssetRegistry: FAssetRegistry took 0.0031 seconds to start up
[2025.08.26-03.08.18:117][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.08.26-03.08.18:197][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.000s loading caches ../../../../../../Game/Auracron/Intermediate/CachedAssetRegistry_*.bin.
[2025.08.26-03.08.18:492][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.08.26-03.08.18:492][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.08.26-03.08.18:557][  0]LogDeviceProfileManager: Active device profile: [0000029E6BA2E100][0000029E577EA000 66] WindowsEditor
[2025.08.26-03.08.18:557][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.08.26-03.08.18:560][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.08.26-03.08.18:569][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.08.26-03.08.18:569][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.08.26-03.08.18:569][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.08.26-03.08.18:581][  0]LogTurnkeySupport: Turnkey Platform: Android: (Status=Invalid, MinAllowed_Sdk=r25b, MaxAllowed_Sdk=r29, Current_Sdk=, Allowed_AutoSdk=r27c, Current_AutoSdk=, Flags="Platform_InvalidHostPrerequisites, Support_FullSdk", Error="Android Studio is not installed correctly.")
[2025.08.26-03.08.18:582][  0]LogTurnkeySupport: Turnkey Platform: IOS: (Status=Invalid, MinAllowed_Sdk=1100.0.0.0, MaxAllowed_Sdk=8999.0, Current_Sdk=, Allowed_AutoSdk=15.2, Current_AutoSdk=, Flags="Platform_ValidHostPrerequisites")
[2025.08.26-03.08.18:582][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.26100.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists")
[2025.08.26-03.08.18:583][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="C:/Game/Auracron/Auracron.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Game/Auracron/Intermediate/TurnkeyReport_1.log" -log="C:/Game/Auracron/Intermediate/TurnkeyLog_1.log" -project="C:/Game/Auracron/Auracron.uproject"  -Device=Win64@TKT'
[2025.08.26-03.08.18:583][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""C:/Program Files/Epic Games/UE_5.6/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="C:/Game/Auracron/Auracron.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="C:/Game/Auracron/Intermediate/TurnkeyReport_1.log" -log="C:/Game/Auracron/Intermediate/TurnkeyLog_1.log" -project="C:/Game/Auracron/Auracron.uproject"  -Device=Win64@TKT" -nocompile -nocompileuat ]
[2025.08.26-03.08.18:625][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.26-03.08.18:626][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness because of a recursive sync load
[2025.08.26-03.08.18:626][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.26-03.08.18:626][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.26-03.08.18:627][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec because of a recursive sync load
[2025.08.26-03.08.18:628][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.26-03.08.18:628][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.26-03.08.18:632][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_M because of a recursive sync load
[2025.08.26-03.08.18:633][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_N because of a recursive sync load
[2025.08.26-03.08.18:635][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Opacity/CameraDepthFade because of a recursive sync load
[2025.08.26-03.08.18:636][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.26-03.08.18:637][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.26-03.08.18:637][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.26-03.08.18:644][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDiffuse because of a recursive sync load
[2025.08.26-03.08.18:644][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components because of a recursive sync load
[2025.08.26-03.08.18:647][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultPostProcessMaterial because of a recursive sync load
[2025.08.26-03.08.18:647][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.26-03.08.18:704][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultLightFunctionMaterial because of a recursive sync load
[2025.08.26-03.08.18:704][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.26-03.08.18:729][  0]LogStreaming: Display: Package /Engine/EngineMaterials/WorldGridMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDeferredDecalMaterial because of a recursive sync load
[2025.08.26-03.08.18:729][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.26-03.08.18:754][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/WorldGridMaterial because of a recursive sync load
[2025.08.26-03.08.18:754][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.26-03.08.18:998][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.08.26-03.08.18:999][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.08.26-03.08.18:999][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.08.26-03.08.18:999][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.08.26-03.08.18:999][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.08.26-03.08.20:469][  0]LogConfig: Applying CVar settings from Section [/Script/CQTest.CQTestSettings] File [Engine]
[2025.08.26-03.08.20:543][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.08.26-03.08.20:543][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.08.26-03.08.20:544][  0]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetActorFactory name: NetActorFactory id: 0
[2025.08.26-03.08.20:544][  0]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetSubObjectFactory name: NetSubObjectFactory id: 1
[2025.08.26-03.08.20:552][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.08.26-03.08.20:552][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.08.26-03.08.20:554][  0]LogLiveCoding: Display: First instance in process group "UE_Auracron_0xa5ca6502", spawning console
[2025.08.26-03.08.20:562][  0]LogLiveCoding: Display: Waiting for server
[2025.08.26-03.08.20:589][  0]LogSlate: Border
[2025.08.26-03.08.20:589][  0]LogSlate: BreadcrumbButton
[2025.08.26-03.08.20:589][  0]LogSlate: Brushes.Title
[2025.08.26-03.08.20:589][  0]LogSlate: ColorPicker.ColorThemes
[2025.08.26-03.08.20:589][  0]LogSlate: Default
[2025.08.26-03.08.20:589][  0]LogSlate: Icons.Save
[2025.08.26-03.08.20:589][  0]LogSlate: Icons.Toolbar.Settings
[2025.08.26-03.08.20:589][  0]LogSlate: ListView
[2025.08.26-03.08.20:589][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.08.26-03.08.20:589][  0]LogSlate: SoftwareCursor_Grab
[2025.08.26-03.08.20:589][  0]LogSlate: TableView.DarkRow
[2025.08.26-03.08.20:589][  0]LogSlate: TableView.Row
[2025.08.26-03.08.20:589][  0]LogSlate: TreeView
[2025.08.26-03.08.20:784][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.08.26-03.08.20:788][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 4.275 ms
[2025.08.26-03.08.20:825][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.08.26-03.08.20:825][  0]LogInit: XR: MultiViewport is Disabled
[2025.08.26-03.08.20:825][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.08.26-03.08.20:874][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.08.26-03.08.21:401][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.08.26-03.08.21:692][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: D594BE08A0F64AAB8000000000002A00 | Instance: C80289F143EFCFC40A65338CEAF8C733 (TKT-30504).
[2025.08.26-03.08.21:770][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.08.26-03.08.21:775][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.08.26-03.08.21:777][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.08.26-03.08.21:777][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:54978'.
[2025.08.26-03.08.21:792][  0]LogUdpMessaging: Display: Added local interface '192.168.0.35' to multicast group '230.0.0.1:6666'
[2025.08.26-03.08.21:792][  0]LogUdpMessaging: Display: Added local interface '172.26.144.1' to multicast group '230.0.0.1:6666'
[2025.08.26-03.08.21:796][  0]LogUdpMessaging: Display: Using asynchronous task graph for message deserialization.
[2025.08.26-03.08.21:984][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.08.26-03.08.21:984][  0]LogNNERuntimeORT: Selecting NPU adapter: Intel(R) Iris(R) Xe Graphics
[2025.08.26-03.08.22:147][  0]LogNNERuntimeORT: MakeRuntimeORTDml:
[2025.08.26-03.08.22:147][  0]LogNNERuntimeORT:   DirectML:  yes
[2025.08.26-03.08.22:147][  0]LogNNERuntimeORT:   RHI D3D12: no
[2025.08.26-03.08.22:147][  0]LogNNERuntimeORT:   D3D12:     yes
[2025.08.26-03.08.22:147][  0]LogNNERuntimeORT:   NPU:       yes
[2025.08.26-03.08.22:147][  0]LogNNERuntimeORT: Interface availability:
[2025.08.26-03.08.22:147][  0]LogNNERuntimeORT:   GPU: yes
[2025.08.26-03.08.22:147][  0]LogNNERuntimeORT:   RDG: no
[2025.08.26-03.08.22:147][  0]LogNNERuntimeORT:   NPU: yes
[2025.08.26-03.08.22:310][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.08.26-03.08.22:310][  0]LogNNERuntimeORT: Selecting NPU adapter: Intel(R) Iris(R) Xe Graphics
[2025.08.26-03.08.22:742][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.08.26-03.08.22:743][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.08.26-03.08.22:798][  0]LogMetaSound: MetaSound Engine Initialized
[2025.08.26-03.08.22:940][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.10.dll
[2025.08.26-03.08.23:026][  0]LogChaosVDEditor: [FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDGenericDebugDrawExtension] ...
[2025.08.26-03.08.23:026][  0]LogChaosVDEditor: [FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDAccelerationStructuresExtension] ...
[2025.08.26-03.08.23:034][  0]LogTimingProfiler: Initialize
[2025.08.26-03.08.23:034][  0]LogTimingProfiler: OnSessionChanged
[2025.08.26-03.08.23:034][  0]LoadingProfiler: Initialize
[2025.08.26-03.08.23:035][  0]LoadingProfiler: OnSessionChanged
[2025.08.26-03.08.23:035][  0]LogNetworkingProfiler: Initialize
[2025.08.26-03.08.23:035][  0]LogNetworkingProfiler: OnSessionChanged
[2025.08.26-03.08.23:035][  0]LogMemoryProfiler: Initialize
[2025.08.26-03.08.23:035][  0]LogMemoryProfiler: OnSessionChanged
[2025.08.26-03.08.23:118][  0]LogConfig: Applying CVar settings from Section [/Script/PCG.PCGEngineSettings] File [Engine]
[2025.08.26-03.08.23:186][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.08.26-03.08.23:430][  0]SourceControl: Controle de revisão desabilitado
[2025.08.26-03.08.23:446][  0]SourceControl: Controle de revisão desabilitado
[2025.08.26-03.08.23:449][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Icons/doc_16x.png' error.
[2025.08.26-03.08.23:449][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Content/Slate/Common/ButtonHoverHint.png' error.
[2025.08.26-03.08.23:449][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png] file: [../../../Engine/Content/Slate/Common/ButtonHoverHint.png]
[2025.08.26-03.08.23:449][  0]LogSlate: Could not load file for Slate resource: [../../../Engine/Content/Slate/Icons/doc_16x.png] file: [../../../Engine/Content/Slate/Icons/doc_16x.png]
[2025.08.26-03.08.24:015][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.08.26-03.08.24:024][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.08.26-03.08.24:040][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.08.26-03.08.24:040][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.08.26-03.08.24:280][  0]LogCollectionManager: Loaded 0 collections in 0.003168 seconds
[2025.08.26-03.08.24:285][  0]LogFileCache: Scanning file cache for directory 'C:/Game/Auracron/Saved/Collections/' took 0.00s
[2025.08.26-03.08.24:289][  0]LogFileCache: Scanning file cache for directory 'C:/Game/Auracron/Content/Developers/tktca/Collections/' took 0.00s
[2025.08.26-03.08.24:295][  0]LogFileCache: Scanning file cache for directory 'C:/Game/Auracron/Content/Collections/' took 0.01s
[2025.08.26-03.08.24:321][  0]LogTemp: Display: UnrealMCPCollisionCommands: Initialized
[2025.08.26-03.08.24:321][  0]LogTemp: UnrealMCPPathfindingCommands initialized. NavigationSystem will be obtained when needed.
[2025.08.26-03.08.24:321][  0]LogTemp: UnrealMCP AI Commands initialized
[2025.08.26-03.08.24:321][  0]LogTemp: FUnrealMCPRealmCommands: Sistema de Transição de Realms inicializado
[2025.08.26-03.08.24:321][  0]LogTemp: FUnrealMCPMultilayerMapCommands: Sistema de Mapa Multicamada inicializado
[2025.08.26-03.08.24:321][  0]LogTemp: FUnrealMCPLaneMechanicsCommands: Sistema de Mecânicas de Lane inicializado
[2025.08.26-03.08.24:321][  0]LogTemp: FUnrealMCPJungleSystemCommands: Sistema de Jungle inicializado
[2025.08.26-03.08.24:321][  0]LogTemp: FUnrealMCPVerticalNavigationCommands: Sistema de Navegação Vertical inicializado
[2025.08.26-03.08.24:321][  0]LogTemp: FUnrealMCPGamePhasesCommands: Sistema de Fases da Partida inicializado
[2025.08.26-03.08.24:321][  0]LogTemp: FUnrealMCPObjectivesStructuresCommands: Sistema de Objetivos e Estruturas inicializado
[2025.08.26-03.08.24:321][  0]LogTemp: FUnrealMCPCombatMechanicsCommands: Sistema de Mecânicas de Combate inicializado
[2025.08.26-03.08.24:321][  0]LogScript: Warning: Script Msg: A null object was passed as a world context object to UEngine::GetWorldFromContextObject().
[2025.08.26-03.08.24:321][  0]LogTemp: Display: Unreal MCP Module has started
[2025.08.26-03.08.24:405][  0]LogUObjectArray: 45742 objects as part of root set at end of initial load.
[2025.08.26-03.08.24:405][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.08.26-03.08.24:633][  0]LogAutomationTest: Error: Condition failed
[2025.08.26-03.08.24:633][  0]LogAutomationTest: Error: Condition failed
[2025.08.26-03.08.24:633][  0]LogAutomationTest: Error: Condition failed
[2025.08.26-03.08.24:633][  0]LogEngine: Initializing Engine...
[2025.08.26-03.08.24:906][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.08.26-03.08.24:907][  0]LogTedsSettings: UTedsSettingsEditorSubsystem::Initialize
[2025.08.26-03.08.24:907][  0]LogTemp: Display: UnrealMCPCollisionCommands: Initialized
[2025.08.26-03.08.24:907][  0]LogTemp: UnrealMCPPathfindingCommands initialized. NavigationSystem will be obtained when needed.
[2025.08.26-03.08.24:907][  0]LogTemp: UnrealMCP AI Commands initialized
[2025.08.26-03.08.24:907][  0]LogTemp: FUnrealMCPRealmCommands: Sistema de Transição de Realms inicializado
[2025.08.26-03.08.24:907][  0]LogTemp: FUnrealMCPMultilayerMapCommands: Sistema de Mapa Multicamada inicializado
[2025.08.26-03.08.24:907][  0]LogTemp: FUnrealMCPLaneMechanicsCommands: Sistema de Mecânicas de Lane inicializado
[2025.08.26-03.08.24:907][  0]LogTemp: FUnrealMCPJungleSystemCommands: Sistema de Jungle inicializado
[2025.08.26-03.08.24:907][  0]LogTemp: FUnrealMCPVerticalNavigationCommands: Sistema de Navegação Vertical inicializado
[2025.08.26-03.08.24:907][  0]LogTemp: FUnrealMCPGamePhasesCommands: Sistema de Fases da Partida inicializado
[2025.08.26-03.08.24:907][  0]LogTemp: FUnrealMCPObjectivesStructuresCommands: Sistema de Objetivos e Estruturas inicializado
[2025.08.26-03.08.24:907][  0]LogTemp: FUnrealMCPCombatMechanicsCommands: Sistema de Mecânicas de Combate inicializado
[2025.08.26-03.08.24:907][  0]LogScript: Warning: Script Msg: A null object was passed as a world context object to UEngine::GetWorldFromContextObject().
[2025.08.26-03.08.24:907][  0]LogTemp: Display: UnrealMCPBridge: Initializing
[2025.08.26-03.08.24:907][  0]LogTemp: Display: UnrealMCPBridge: Server started on 127.0.0.1:55557
[2025.08.26-03.08.24:907][  0]LogTemp: Display: MCPServerRunnable: Created server runnable
[2025.08.26-03.08.24:913][  0]LogTemp: Display: MCPServerRunnable: Server thread starting...
[2025.08.26-03.08.25:880][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.08.26-03.08.26:017][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.08.26-03.08.26:089][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.08.26-03.08.26:110][  0]LogNetVersion: Set ProjectVersion to *******. Version Checksum will be recalculated on next use.
[2025.08.26-03.08.26:110][  0]LogInit: Texture streaming: Enabled
[2025.08.26-03.08.26:120][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] AnalyticsET::StartSession ( APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.6.1-44394996+++UE5+Release-5.6 )
[2025.08.26-03.08.26:125][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.08.26-03.08.26:138][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.08.26-03.08.26:139][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.08.26-03.08.26:140][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.08.26-03.08.26:141][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.08.26-03.08.26:141][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.08.26-03.08.26:141][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.08.26-03.08.26:141][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.08.26-03.08.26:141][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.08.26-03.08.26:141][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.08.26-03.08.26:141][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.08.26-03.08.26:141][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.08.26-03.08.26:141][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.08.26-03.08.26:141][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.08.26-03.08.26:141][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.08.26-03.08.26:141][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.08.26-03.08.26:153][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.08.26-03.08.27:262][  0]LogAudioMixer: Display: Using Audio Hardware Device Colunas (Realtek(R) Audio)
[2025.08.26-03.08.27:264][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.08.26-03.08.27:269][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.08.26-03.08.27:269][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.08.26-03.08.27:271][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.08.26-03.08.27:271][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.08.26-03.08.27:275][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.08.26-03.08.27:275][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.08.26-03.08.27:276][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.08.26-03.08.27:276][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.08.26-03.08.27:276][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.08.26-03.08.27:283][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.08.26-03.08.27:291][  0]LogInit: Undo buffer set to 256 MB
[2025.08.26-03.08.27:292][  0]LogInit: Transaction tracking system initialized
[2025.08.26-03.08.27:308][  0]LogSourceControl: Display: Uncontrolled Changelist persistency file loaded ../../../../../../Game/Auracron/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.26-03.08.27:376][  0]LocalizationService: O serviço de localização está desativado.
[2025.08.26-03.08.27:450][  0]LogTurnkeySupport: Turnkey Device: Win64@tkt: (Name=tkt, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.26100.0, Flags="Device_InstallSoftwareValid")
[2025.08.26-03.08.28:199][  0]LogFileCache: Scanning file cache for directory 'C:/Game/Auracron/Content/' took 0.00s
[2025.08.26-03.08.28:229][  0]LogNNEDenoiser: Ray Tracing is not enabled, therefore NNEDenoiser is not registered!
[2025.08.26-03.08.28:317][  0]LogPython: Python enabled via CVar 'Engine.Python.IsEnabledByDefault'
[2025.08.26-03.08.28:319][  0]LogPython: Using Python 3.11.8
[2025.08.26-03.08.28:376][  0]LogPython: Display: No pip-enabled plugins with python dependencies found, skipping
[2025.08.26-03.08.29:253][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.08.26-03.08.29:305][  0]LogEditorDataStorage: Initializing
[2025.08.26-03.08.29:311][  0]LogEditorDataStorage: Initialized
[2025.08.26-03.08.29:316][  0]LogCore: Display: GameplayInsights module auto-connecting to local trace server...
[2025.08.26-03.08.29:318][  0]LogGameplayAbilityAudit: Selected GameplayAbilityAuditRow as the best Gameplay Ability Audit Functionality
[2025.08.26-03.08.29:467][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.08.26-03.08.29:471][  0]SourceControl: Controle de revisão desabilitado
[2025.08.26-03.08.29:471][  0]LogUnrealEdMisc: Loading editor; pre map load, took 40.355
[2025.08.26-03.08.29:473][  0]Cmd: MAP LOAD FILE="../../../Engine/Content/Maps/Templates/OpenWorld.umap" TEMPLATE=1 SHOWPROGRESS=1 FEATURELEVEL=3
[2025.08.26-03.08.29:476][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.08.26-03.08.29:477][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.08.26-03.08.29:505][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.08.26-03.08.29:509][  0]LogUObjectHash: Compacting FUObjectHashTables data took   1.13ms
[2025.08.26-03.08.29:611][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled_1'.
[2025.08.26-03.08.29:611][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled_1
[2025.08.26-03.08.29:614][  0]LogWorldPartition: ULevel::OnLevelLoaded(Untitled_1)(bIsOwningWorldGameWorld=0, bIsOwningWorldPartitioned=1, InitializeForMainWorld=1, InitializeForEditor=1, InitializeForGame=0)
[2025.08.26-03.08.29:614][  0]LogWorldPartition: Display: WorldPartition initialize started...
[2025.08.26-03.08.29:614][  0]LogWorldPartition: UWorldPartition::Initialize : World = /Temp/Untitled_1.Untitled_1, World Type = Editor, IsMainWorldPartition = 1, Location = V(0), Rotation = R(0), IsEditor = 1, IsGame = 0, IsPIEWorldTravel = 0, IsCooking = 0
[2025.08.26-03.08.29:637][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/0/IT/MHSUN7EJKDAAL8CVT31B9U which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.29:768][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/0/IT/MHSUN7EJKDAAL8CVT31B9U which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.29:914][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/7/OK/SEAN2F64CHO3LDZ0AH36XQ which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.29:960][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/8/MI/8FR6U8QWRABNNBFVHWOAWB which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.29:961][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/4/1N/5TOBNLYN25C967WMQGLJ9P which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.29:963][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/2/2W/ANLD5XYRRTSZ6KDZ50Y9DT which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.29:964][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/3/MJ/0VP5T9S3Z20RD9I6IIKQWQ which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:018][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/3/NZ/HCXCEB2P7AFAQJ2T4V3UNL which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:078][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/B/DH/CGZKHGOFN0M7BUZD4OUIKT which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:079][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/4/PC/3GY1SKTEHE8BAYQDGV30M1 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:080][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/A/9Z/3O1J47LASQDXUEAV2FHEBM which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:082][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/1/K5/N01XJYY7OZ33C3E6ES5YS7 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:083][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/1/NJ/82MLNKCEZ5S5X05ZNVY7W4 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:140][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/7/P5/HN2K8ZKQ2F8MNDYTNDLO09 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:197][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/7/SI/H1IC2U6KA5SQCGX13AM5C6 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:251][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/B/RG/YU7NN5B9QJO6YF5ZTG8XC8 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:252][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/E/SI/TI40SGT0J4D63RIQKYYMXM which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:253][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/5/3V/7BKY2RMHFQPXE7ZF1XJ0U9 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:255][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/D/XC/NCQVN0MLHWDXR4YK17HE2J which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:255][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/3/XF/OQLKUQUBT4P1A33FIS1EJ6 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:257][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/3/V8/DG1H4UL6K2RINN5XMKLRZA which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:313][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/2/FA/49ZR6ZHH8CF2C1JD77Q4ED which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:369][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/4/RT/MG15C0UXXQRV0JVWRQJISZ which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:370][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/7/VJ/9LQX4A09CNM8TATIA1T7NH which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:371][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/D/3G/YQB0MZ4557X9DVE0F0I8QE which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:372][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/8/E9/CFPXJNMC0901PIWNMJDXPL which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:431][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/9/9M/76V7GBFY28HAZWO7KGINSN which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:432][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/2/5R/L5HRF36RXYPBGTS0FD6B9B which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:434][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/4/40/UOXBXHJRWZ17KVKFQOUS39 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:488][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/8/4E/6X2LF5M4GJACKSAZWHMMYZ which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:550][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/D/X7/S2RERO642Z9R49WW6ZE4KV which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:611][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/6/OO/2HJONZ0FWQR1O115ILCF4T which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:612][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/4/O6/RV7D86FR4JHF2UE90HW07M which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:613][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/2/P2/U7UGH6COIE3JDYT7437DSA which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:669][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/2/ML/11T6R7R7BS5ZZAEQ598G6Q which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:671][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/2/NP/19NSXDGR5APGMOCHXFR8UK which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:672][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/E/E8/NG6H540VO5IXS4SM1TGGKO which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:673][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/0/SB/0L7PJTYD6VY7AGOJQ4Q6LN which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:674][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/1/W7/WOIJ2EO34NCL8X2XRY0R2Q which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:675][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/3/37/ZC3EFDZU905UZGCPVNO6NL which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:728][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/0/DE/HPJ5D6HFXBQGR5KE6BFEOG which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:729][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/0/03/DBBOQZJVVEJB2VEJNQV1KH which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:730][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/D/YR/I017Y1HLOS635ZMP2TLHJX which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:731][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/9/OG/GRLOHBEDGLRDJ7ALVKWBR4 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:732][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/B/AQ/T365MTIZ0AHKXDX3030T91 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:733][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/B/ML/JJ2YHS3IUKXQ25T5AKH56J which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:789][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/5/C8/GKZM278JVRCZYA95405LY7 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:790][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/7/7Y/G4WW8N40GC4GTU6EVBCMSZ which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:791][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/A/21/LIJ7LP5MUAYCOMJNBYVTPT which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:793][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/2/ST/K4A6UNOD93F0RPUUIEE5U1 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:846][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/6/4N/DN4AWK66I9SFKU5KMAW740 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:848][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/2/T0/3RL7RZ5V18GXKIZMFMJOUF which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:901][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/B/ZM/YHAGTLGWJ9T0072PI04NG3 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:902][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/D/FH/YEQQOIJ3BNU7FZDRKVUZZO which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:903][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/7/MK/TEXD9D1LJ6YPCG6SL30VHN which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:904][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/D/02/XDM669NAU8NSRA6FV796CE which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:906][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/4/J0/IZ97T3DS224ZE5JNLUBNUV which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:960][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/D/R2/J0MMRZ3V2RDPN33J2S33ZJ which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:961][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/1/9D/YYFHNASO3MR7RH8G7M8YKS which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:962][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/2/IN/38GJJAUSXTLENXKEDEFGSU which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.30:964][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/5/37/H44BYI66ITH1M56VG9XQV4 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:028][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/1/JS/VRUDS8N9VA2GUST0U3PQWG which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:083][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/8/10/J0H3C3FLUMY3GRTEATZH50 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:085][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/C/I9/AJ6BGNL2CLVLI1RTL9FO2L which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:141][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/4/S4/XACO1KKMI66CBFJN9H2BRO which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:197][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/B/FS/YEUH0KPAUSFGKST2H2XGZJ which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:208][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.26-03.08.31:221][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.26-03.08.31:230][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.26-03.08.31:238][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [AppleTV]
[2025.08.26-03.08.31:238][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [IOS]
[2025.08.26-03.08.31:238][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.26-03.08.31:245][  0]LogDeviceProfileManager: Display: Deviceprofile None not found.
[2025.08.26-03.08.31:466][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/7/OK/SEAN2F64CHO3LDZ0AH36XQ which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:511][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/8/MI/8FR6U8QWRABNNBFVHWOAWB which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:511][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/4/1N/5TOBNLYN25C967WMQGLJ9P which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:512][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/2/2W/ANLD5XYRRTSZ6KDZ50Y9DT which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:512][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/3/MJ/0VP5T9S3Z20RD9I6IIKQWQ which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:512][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/3/NZ/HCXCEB2P7AFAQJ2T4V3UNL which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:512][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/B/DH/CGZKHGOFN0M7BUZD4OUIKT which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:512][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/4/PC/3GY1SKTEHE8BAYQDGV30M1 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:512][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/A/9Z/3O1J47LASQDXUEAV2FHEBM which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:512][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/1/K5/N01XJYY7OZ33C3E6ES5YS7 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:564][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/1/NJ/82MLNKCEZ5S5X05ZNVY7W4 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:564][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/7/P5/HN2K8ZKQ2F8MNDYTNDLO09 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:564][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/7/SI/H1IC2U6KA5SQCGX13AM5C6 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:564][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/B/RG/YU7NN5B9QJO6YF5ZTG8XC8 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:564][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/E/SI/TI40SGT0J4D63RIQKYYMXM which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:564][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/5/3V/7BKY2RMHFQPXE7ZF1XJ0U9 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:564][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/D/XC/NCQVN0MLHWDXR4YK17HE2J which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:564][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/3/XF/OQLKUQUBT4P1A33FIS1EJ6 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:564][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/3/V8/DG1H4UL6K2RINN5XMKLRZA which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/2/FA/49ZR6ZHH8CF2C1JD77Q4ED which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/4/RT/MG15C0UXXQRV0JVWRQJISZ which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/7/VJ/9LQX4A09CNM8TATIA1T7NH which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/D/3G/YQB0MZ4557X9DVE0F0I8QE which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/8/E9/CFPXJNMC0901PIWNMJDXPL which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/9/9M/76V7GBFY28HAZWO7KGINSN which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/2/5R/L5HRF36RXYPBGTS0FD6B9B which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/4/40/UOXBXHJRWZ17KVKFQOUS39 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/8/4E/6X2LF5M4GJACKSAZWHMMYZ which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/D/X7/S2RERO642Z9R49WW6ZE4KV which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/6/OO/2HJONZ0FWQR1O115ILCF4T which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/4/O6/RV7D86FR4JHF2UE90HW07M which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/2/P2/U7UGH6COIE3JDYT7437DSA which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/2/ML/11T6R7R7BS5ZZAEQ598G6Q which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/2/NP/19NSXDGR5APGMOCHXFR8UK which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/E/E8/NG6H540VO5IXS4SM1TGGKO which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/0/SB/0L7PJTYD6VY7AGOJQ4Q6LN which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/1/W7/WOIJ2EO34NCL8X2XRY0R2Q which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/3/37/ZC3EFDZU905UZGCPVNO6NL which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/0/DE/HPJ5D6HFXBQGR5KE6BFEOG which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/0/03/DBBOQZJVVEJB2VEJNQV1KH which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/D/YR/I017Y1HLOS635ZMP2TLHJX which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/9/OG/GRLOHBEDGLRDJ7ALVKWBR4 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/B/AQ/T365MTIZ0AHKXDX3030T91 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/B/ML/JJ2YHS3IUKXQ25T5AKH56J which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/5/C8/GKZM278JVRCZYA95405LY7 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/7/7Y/G4WW8N40GC4GTU6EVBCMSZ which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/A/21/LIJ7LP5MUAYCOMJNBYVTPT which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/2/ST/K4A6UNOD93F0RPUUIEE5U1 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/6/4N/DN4AWK66I9SFKU5KMAW740 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/2/T0/3RL7RZ5V18GXKIZMFMJOUF which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/B/ZM/YHAGTLGWJ9T0072PI04NG3 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/D/FH/YEQQOIJ3BNU7FZDRKVUZZO which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/7/MK/TEXD9D1LJ6YPCG6SL30VHN which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/D/02/XDM669NAU8NSRA6FV796CE which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/4/J0/IZ97T3DS224ZE5JNLUBNUV which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/D/R2/J0MMRZ3V2RDPN33J2S33ZJ which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/1/9D/YYFHNASO3MR7RH8G7M8YKS which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/2/IN/38GJJAUSXTLENXKEDEFGSU which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/5/37/H44BYI66ITH1M56VG9XQV4 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/1/JS/VRUDS8N9VA2GUST0U3PQWG which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/8/10/J0H3C3FLUMY3GRTEATZH50 which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/C/I9/AJ6BGNL2CLVLI1RTL9FO2L which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/4/S4/XACO1KKMI66CBFJN9H2BRO which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:565][  0]LogAssetRegistry: SetPropertiesAndWait called on C:/Game/Auracron/Saved/Untitled_1_InstanceOf_/Engine/__ExternalActors__/Maps/Templates/OpenWorld/B/FS/YEUH0KPAUSFGKST2H2XGZJ which is not in a mounted directory. Call will be ignored.
[2025.08.26-03.08.31:566][  0]LogWorldPartition: Display: WorldPartition initialize took 1.95 sec
[2025.08.26-03.08.31:996][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.08.26-03.08.32:022][  0]LogUObjectHash: Compacting FUObjectHashTables data took   1.08ms
[2025.08.26-03.08.32:022][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.08.26-03.08.32:026][  0]MapCheck: Verificação do mapa concluída: 0 erro(s), 0 aviso(s), levou 2,58ms para ser concluída.
[2025.08.26-03.08.32:044][  0]LogUnrealEdMisc: Total Editor Startup Time, took 42.928
[2025.08.26-03.08.32:190][  0]LogPlacementMode: Display: The Asset Registry is not yet fully loaded so some placeable classes might be missing.
[2025.08.26-03.08.32:231][  0]LogSlate: The tab "TopLeftModeTab" attempted to spawn in layout 'LevelEditor_Layout_v1.8' but failed for some reason. It will not be displayed.
[2025.08.26-03.08.32:401][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.26-03.08.32:556][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.26-03.08.32:703][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.26-03.08.32:842][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.26-03.08.33:003][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.26-03.08.33:003][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.08.26-03.08.33:004][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.26-03.08.33:004][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.08.26-03.08.33:005][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.26-03.08.33:005][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.08.26-03.08.33:006][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.26-03.08.33:006][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.08.26-03.08.33:006][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.26-03.08.33:007][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.08.26-03.08.33:007][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.26-03.08.33:008][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.08.26-03.08.33:009][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.26-03.08.33:009][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.08.26-03.08.33:009][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.26-03.08.33:010][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.08.26-03.08.33:010][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.26-03.08.33:010][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.08.26-03.08.33:011][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.26-03.08.33:011][  0]LogPakFile: Display: Mounted Pak file 'C:/Program Files/Epic Games/UE_5.6/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.08.26-03.08.33:544][  0]LogSlate: Took 0.000556 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.08.26-03.08.33:553][  0]LogSlate: Took 0.000264 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.08.26-03.08.33:815][  0]LogSlate: Took 0.000515 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.08.26-03.08.34:085][  0]LogStall: Startup...
[2025.08.26-03.08.34:093][  0]LogStall: Startup complete.
[2025.08.26-03.08.34:101][  0]LogLoad: (Engine Initialization) Total time: 44.99 seconds
[2025.08.26-03.08.35:082][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.08.26-03.08.35:431][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.08.26-03.08.35:431][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.08.26-03.08.35:497][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.08.26-03.08.35:498][  0]LogPython: Display: Executando código de inicialização C:/Program Files/Epic Games/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.08.26-03.08.35:545][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.08.26-03.08.35:552][  0]LogPython: Display: Executando código de inicialização C:/Program Files/Epic Games/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 54.150 ms
[2025.08.26-03.08.35:971][  1]LogAssetRegistry: Display: Asset registry cache written as 73.1 MiB to ../../../../../../Game/Auracron/Intermediate/CachedAssetRegistry_*.bin
[2025.08.26-03.08.36:154][  2]LogAssetRegistry: AssetRegistryGather time 16.6319s: AssetDataDiscovery 0.1506s, AssetDataGather 16.3978s, StoreResults 0.0835s. Wall time 18.0410s.
	NumCachedDirectories 0. NumUncachedDirectories 1448. NumCachedFiles 0. NumUncachedFiles 7466.
	BackgroundTickInterruptions 0.
[2025.08.26-03.08.36:173][  2]LogPlacementMode: Display: The Asset Registry is done with its initial scan, the list of placeable classes has been updated.
[2025.08.26-03.08.36:182][  2]LogStreaming: Display: FlushAsyncLoading(473): 1 QueuedPackages, 0 AsyncPackages
[2025.08.26-03.08.36:185][  2]LogCollectionManager: Fixed up redirectors for 0 collections in 0.000000 seconds (updated 0 objects)
[2025.08.26-03.08.36:185][  2]LogSourceControl: Uncontrolled asset discovery started...
[2025.08.26-03.08.36:442][  4]LogSourceControl: Uncontrolled asset discovery finished in 0.255858 seconds (Found 7442 uncontrolled assets)
[2025.08.26-03.08.46:485][ 34]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.08.46:485][ 34]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.08.46:485][ 34]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.08.46:586][ 34]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.08.46:587][ 34]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.08.46:587][ 34]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "AuracronMultilayerManager", "type": "MULTILAYERMANAGER", "location": [0.0, 0.0, 0.0], "rotation": [0.0, 0.0, 0.0]}}
[2025.08.26-03.08.46:587][ 34]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.26-03.08.46:595][ 34]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Unknown actor type: MULTILAYERMANAGER"
}
[2025.08.26-03.08.46:596][ 34]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 77
[2025.08.26-03.08.46:596][ 34]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.08.53:381][ 77]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.08.53:381][ 77]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.08.53:381][ 77]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-03.08.53:481][ 78]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.08.53:482][ 78]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.08.53:482][ 78]LogTemp: Display: MCPServerRunnable: Received: {"type": "spawn_actor", "params": {"name": "AuracronMultilayerManager", "type": "AMULTILAYERMANAGER", "location": [0.0, 0.0, 0.0], "rotation": [0.0, 0.0, 0.0]}}
[2025.08.26-03.08.53:482][ 78]LogTemp: Display: UnrealMCPBridge: Executing command: spawn_actor
[2025.08.26-03.08.53:741][ 78]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Unknown actor type: AMULTILAYERMANAGER"
}
[2025.08.26-03.08.53:741][ 78]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 78
[2025.08.26-03.08.53:741][ 78]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.09.00:535][139]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.09.00:535][139]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.09.00:535][139]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-03.09.00:636][145]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.09.00:636][145]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.09.00:636][145]LogTemp: Display: MCPServerRunnable: Received: {"type": "focus_viewport", "params": {"target": "PlanicieRadianteFloor", "distance": 1500.0}}
[2025.08.26-03.09.00:636][145]LogTemp: Display: UnrealMCPBridge: Executing command: focus_viewport
[2025.08.26-03.09.00:646][145]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "error",
	"error": "Actor not found: PlanicieRadianteFloor"
}
[2025.08.26-03.09.00:646][146]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 78
[2025.08.26-03.09.00:646][146]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.09.06:677][195]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.09.06:677][195]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.09.06:677][195]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-03.09.06:778][195]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.09.06:778][195]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.09.06:778][195]LogTemp: Display: MCPServerRunnable: Received: {"type": "get_actors_in_level", "params": {}}
[2025.08.26-03.09.06:778][195]LogTemp: Display: UnrealMCPBridge: Executing command: get_actors_in_level
[2025.08.26-03.09.06:887][195]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"actors": [
			{
				"name": "WorldSettings",
				"class": "WorldSettings",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Brush_0",
				"class": "Brush",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "WorldDataLayers",
				"class": "WorldDataLayers",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "MassVisualizer_0",
				"class": "MassVisualizer",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "DefaultPhysicsVolume_0",
				"class": "DefaultPhysicsVolume",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "GameplayDebuggerPlayerManager_0",
				"class": "GameplayDebuggerPlayerManager",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "ChaosDebugDrawActor",
				"class": "Actor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "WorldPartitionMiniMap_UAID_3497F631D5890DD300_1296925927",
				"class": "WorldPartitionMiniMap",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "DirectionalLight_UAID_A85E45CFE40401D200_1470382761",
				"class": "DirectionalLight",
				"location": [ 0, 0, 0 ],
				"rotation": [ -16, 44, 112 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "StaticMeshActor_UAID_A4AE111137DC54FB00_1240666663",
				"class": "StaticMeshActor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 400, 400, 400 ]
			},
			{
				"name": "SkyAtmosphere_UAID_A85E45CFE40401D200_1470382762",
				"class": "SkyAtmosphere",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "SkyLight_UAID_A85E45CFE40401D200_1470380759",
				"class": "SkyLight",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "PlayerStart_UAID_F02F74551BF5599B01_1153002503",
				"class": "PlayerStart",
				"location": [ -200, 0, 92 ],
				"rotation": [ 0, 180, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "ExponentialHeightFog_UAID_A85E45CFE40401D200_1470382763",
				"class": "ExponentialHeightFog",
				"location": [ -5600, -50, -6850 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "VolumetricCloud_UAID_A85E45CFE40401D200_1470381760",
				"class": "VolumetricCloud",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "Landscape_UAID_A85E45CFE404FBD100_1221515703",
				"class": "Landscape",
				"location": [ -100800, -100800, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_3_2_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -25200, -50400, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_3_3_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -25200, -25200, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_4_3_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 0, -25200, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_3_5_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -25200, 25200, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_3_4_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -25200, 0, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_4_5_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 0, 25200, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_4_4_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 0, 0, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_0_0_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -100800, -100800, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_0_1_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -100800, -75600, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_1_0_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -75600, -100800, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_1_1_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -75600, -75600, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_0_2_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -100800, -50400, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_0_3_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -100800, -25200, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_1_2_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -75600, -50400, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_1_3_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -75600, -25200, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_2_0_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -50400, -100800, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_2_1_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -50400, -75600, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_3_0_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -25200, -100800, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_3_1_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -25200, -75600, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_2_2_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -50400, -50400, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_2_3_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -50400, -25200, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_5_0_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 25200, -100800, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_4_0_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 0, -100800, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_4_1_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 0, -75600, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_5_1_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 25200, -75600, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_6_0_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 50400, -100800, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_6_1_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 50400, -75600, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_5_2_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 25200, -50400, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_4_2_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 0, -50400, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_5_3_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 25200, -25200, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_6_2_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 50400, -50400, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_6_3_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 50400, -25200, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_7_0_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 75600, -100800, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_7_1_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 75600, -75600, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_7_2_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 75600, -50400, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_7_3_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 75600, -25200, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_0_5_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -100800, 25200, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_0_4_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -100800, 0, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_0_6_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -100800, 50400, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_1_5_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -75600, 25200, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_1_4_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -75600, 0, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_1_6_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -75600, 50400, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_0_7_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -100800, 75600, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_1_7_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -75600, 75600, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_2_4_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -50400, 0, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_2_5_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -50400, 25200, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_2_6_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -50400, 50400, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_3_6_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -25200, 50400, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_2_7_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -50400, 75600, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_3_7_0",
				"class": "LandscapeStreamingProxy",
				"location": [ -25200, 75600, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_5_5_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 25200, 25200, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_5_4_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 25200, 0, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_4_6_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 0, 50400, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_5_6_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 25200, 50400, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_6_4_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 50400, 0, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_6_5_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 50400, 25200, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_6_6_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 50400, 50400, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_4_7_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 0, 75600, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_5_7_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 25200, 75600, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_6_7_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 50400, 75600, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_7_4_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 75600, 0, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_7_5_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 75600, 25200, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_7_6_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 75600, 50400, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "LandscapeStreamingProxy_D7T4VF4LBP34PHV9XOEVM5TIG_1_7_7_0",
				"class": "LandscapeStreamingProxy",
				"location": [ 75600, 75600, 100 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 100, 100, 100 ]
			},
			{
				"name": "SmartObjectSubsystemRenderingActor_0",
				"class": "SmartObjectSubsystemRenderingActor",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "AbstractNavData-Default",
				"class": "AbstractNavData",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "HLOD0_Instancing_00000000e432d768",
				"class": "WorldPartitionHLOD",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "HLOD0_Instancing_00000000e4e3cadb",
				"class": "WorldPartitionHLOD",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "HLOD0_Instancing_000000000c2d2848",
				"class": "WorldPartitionHLOD",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "HLOD0_Instancing_00000000b5e934d3",
				"class": "WorldPartitionHLOD",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "HLOD0_Instancing_000000003808e17f",
				"class": "WorldPartitionHLOD",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "HLOD0_Instancing_000000005bb1b7e3",
				"class": "WorldPartitionHLOD",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "HLOD0_Instancing_00000000bbb6ad66",
				"class": "WorldPartitionHLOD",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "HLOD0_Instancing_00000000dfa516b7",
				"class": "WorldPartitionHLOD",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "HLOD0_Instancing_00000000caf01cac",
				"class": "WorldPartitionHLOD",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "HLOD0_Instancing_000000009757b31d",
				"class": "WorldPartitionHLOD",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "HLOD0_Instancing_00000000e2b069c3",
				"class": "WorldPartitionHLOD",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "HLOD0_Instancing_00000000056e4d2f",
				"class": "WorldPartitionHLOD",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "HLOD0_Instancing_00000000c67dd055",
				"class": "WorldPartitionHLOD",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "HLOD0_Instancing_000000008b2c1b91",
				"class": "WorldPartitionHLOD",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "HLOD0_Instancing_000000006d8a60a8",
				"class": "WorldPartitionHLOD",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "HLOD0_Instancing_00000000daffb3b1",
				"class": "WorldPartitionHLOD",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "HLOD0_Instancing_000000004c66ee4e",
				"class": "WorldPartitionHLOD",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			},
			{
				"name": "HLOD0_Instancing_00000000f083c001",
				"class": "WorldPartitionHLOD",
				"location": [ 0, 0, 0 ],
				"rotation": [ 0, 0, 0 ],
				"scale": [ 1, 1, 1 ]
			}
		],
		"total_actors": 146,
		"returned_actors": 100,
		"limited": true
	}
}
[2025.08.26-03.09.06:891][195]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 21566
[2025.08.26-03.09.06:891][195]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.09.15:849][286]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.09.15:849][286]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.09.15:849][286]LogTemp: Warning: MCPServerRunnable: Client disconnected or error. Last error code: 0
[2025.08.26-03.09.15:950][287]LogTemp: Display: MCPServerRunnable: Client connection pending, accepting...
[2025.08.26-03.09.15:950][287]LogTemp: Display: MCPServerRunnable: Client connection accepted
[2025.08.26-03.09.15:950][287]LogTemp: Display: MCPServerRunnable: Received: {"type": "focus_viewport", "params": {"target": "StaticMeshActor_UAID_A4AE111137DC54FB00_1240666663", "distance": 2000.0}}
[2025.08.26-03.09.15:950][287]LogTemp: Display: UnrealMCPBridge: Executing command: focus_viewport
[2025.08.26-03.09.15:951][287]LogTemp: Display: MCPServerRunnable: Sending response: {
	"status": "success",
	"result":
	{
		"success": true
	}
}
[2025.08.26-03.09.15:951][287]LogTemp: Display: MCPServerRunnable: Response sent successfully, bytes: 67
[2025.08.26-03.09.15:951][287]LogTemp: Display: MCPServerRunnable: Client disconnected (zero bytes)
[2025.08.26-03.09.57:690][551]LogSlate: Took 0.000349 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.08.26-03.10.01:653][589]LogStreaming: Display: FlushAsyncLoading(479): 1 QueuedPackages, 0 AsyncPackages
[2025.08.26-03.10.01:902][595]LogActorFactory: Actor Factory attempting to spawn BlueprintGeneratedClass /Game/Blueprints/BP_MultilayerManager.BP_MultilayerManager_C
[2025.08.26-03.10.01:902][595]LogActorFactory: Actor Factory attempting to spawn BlueprintGeneratedClass /Game/Blueprints/BP_MultilayerManager.BP_MultilayerManager_C
[2025.08.26-03.10.01:911][595]LogActorFactory: Actor Factory spawned Blueprint /Game/Blueprints/BP_MultilayerManager.BP_MultilayerManager as actor: BP_MultilayerManager_C /Temp/Untitled_1.Untitled_1:PersistentLevel.BP_MultilayerManager_C_0
[2025.08.26-03.10.01:914][595]LogActorFactory: Actor Factory spawned Blueprint /Game/Blueprints/BP_MultilayerManager.BP_MultilayerManager as actor: BP_MultilayerManager_C /Temp/Untitled_1.Untitled_1:PersistentLevel.BP_MultilayerManager_C_0
[2025.08.26-03.10.01:922][595]LogActorFactory: Loading ActorFactory Class /Script/Engine.LevelInstance
[2025.08.26-03.10.03:141][621]LogUObjectHash: Compacting FUObjectHashTables data took   0.71ms
[2025.08.26-03.10.03:143][621]LogEditorActor: Deleted 0 Actors (0.107 secs)
[2025.08.26-03.10.03:153][621]LogActorFactory: Actor Factory attempting to spawn BlueprintGeneratedClass /Game/Blueprints/BP_MultilayerManager.BP_MultilayerManager_C
[2025.08.26-03.10.03:153][621]LogActorFactory: Actor Factory attempting to spawn BlueprintGeneratedClass /Game/Blueprints/BP_MultilayerManager.BP_MultilayerManager_C
[2025.08.26-03.10.03:159][621]LogActorFactory: Actor Factory spawned Blueprint /Game/Blueprints/BP_MultilayerManager.BP_MultilayerManager as actor: BP_MultilayerManager_C /Temp/Untitled_1.Untitled_1:PersistentLevel.BP_MultilayerManager_C_UAID_7486E2FB865CC28802_2087282255
[2025.08.26-03.10.03:160][621]LogActorFactory: Actor Factory spawned Blueprint /Game/Blueprints/BP_MultilayerManager.BP_MultilayerManager as actor: BP_MultilayerManager_C /Temp/Untitled_1.Untitled_1:PersistentLevel.BP_MultilayerManager_C_UAID_7486E2FB865CC28802_2087282255
[2025.08.26-03.10.16:352][732]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.08.26-03.14.53:062][627]LogActorFactory: Actor Factory attempting to spawn BlueprintGeneratedClass /Game/Blueprints/BP_MultilayerManager.BP_MultilayerManager_C
[2025.08.26-03.14.53:062][627]LogActorFactory: Actor Factory attempting to spawn BlueprintGeneratedClass /Game/Blueprints/BP_MultilayerManager.BP_MultilayerManager_C
[2025.08.26-03.14.53:067][627]LogActorFactory: Actor Factory spawned Blueprint /Game/Blueprints/BP_MultilayerManager.BP_MultilayerManager as actor: BP_MultilayerManager_C /Temp/Untitled_1.Untitled_1:PersistentLevel.BP_MultilayerManager_C_1
[2025.08.26-03.14.53:068][627]LogActorFactory: Actor Factory spawned Blueprint /Game/Blueprints/BP_MultilayerManager.BP_MultilayerManager as actor: BP_MultilayerManager_C /Temp/Untitled_1.Untitled_1:PersistentLevel.BP_MultilayerManager_C_1
[2025.08.26-03.14.54:203][647]LogUObjectHash: Compacting FUObjectHashTables data took   0.89ms
[2025.08.26-03.14.54:205][647]LogEditorActor: Deleted 0 Actors (0.038 secs)
[2025.08.26-03.14.54:216][647]LogActorFactory: Actor Factory attempting to spawn BlueprintGeneratedClass /Game/Blueprints/BP_MultilayerManager.BP_MultilayerManager_C
[2025.08.26-03.14.54:216][647]LogActorFactory: Actor Factory attempting to spawn BlueprintGeneratedClass /Game/Blueprints/BP_MultilayerManager.BP_MultilayerManager_C
[2025.08.26-03.14.54:219][647]LogActorFactory: Actor Factory spawned Blueprint /Game/Blueprints/BP_MultilayerManager.BP_MultilayerManager as actor: BP_MultilayerManager_C /Temp/Untitled_1.Untitled_1:PersistentLevel.BP_MultilayerManager_C_UAID_7486E2FB865CC38802_1304601432
[2025.08.26-03.14.54:220][647]LogActorFactory: Actor Factory spawned Blueprint /Game/Blueprints/BP_MultilayerManager.BP_MultilayerManager as actor: BP_MultilayerManager_C /Temp/Untitled_1.Untitled_1:PersistentLevel.BP_MultilayerManager_C_UAID_7486E2FB865CC38802_1304601432
[2025.08.26-03.15.02:588][951]Cmd: DELETE
[2025.08.26-03.15.02:590][951]Cmd: ACTOR DELETE
[2025.08.26-03.15.02:607][951]LogEditorActor: Deleted Actor: BP_MultilayerManager_C
[2025.08.26-03.15.02:633][951]LogUObjectHash: Compacting FUObjectHashTables data took   0.53ms
[2025.08.26-03.15.02:636][951]LogEditorActor: Deleted 1 Actors (0.036 secs)
[2025.08.26-03.18.34:368][188]LogUObjectHash: Compacting FUObjectHashTables data took   0.65ms
[2025.08.26-03.18.34:373][188]Cmd: OBJ SAVEPACKAGE PACKAGE="/Temp/Untitled_1" FILE="../../../../../../Game/Auracron/Saved/Autosaves/Temp/Untitled_1_Auto1.umap" SILENT=true AUTOSAVING=true KEEPDIRTY=false
[2025.08.26-03.18.34:394][188]LogSavePackage: Moving output files for package: /Temp/Autosaves/Temp/Untitled_1_Auto1
[2025.08.26-03.18.34:394][188]LogSavePackage: Moving '../../../../../../Game/Auracron/Saved/Untitled_1_Auto1CE825C3C4BCC763944F6C287A71E6C2B.tmp' to '../../../../../../Game/Auracron/Saved/Autosaves/Temp/Untitled_1_Auto1.umap'
[2025.08.26-03.18.34:398][188]LogFileHelpers: Editor autosave (incl. external actors) for '/Temp/Untitled_1' took 0.052
[2025.08.26-03.18.34:398][188]LogFileHelpers: Editor autosave (incl. sublevels & external actors) for all levels took 0.052
